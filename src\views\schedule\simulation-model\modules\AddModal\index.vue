<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="880"
    :maskClosable="false"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <a-steps :current="step" size="small" style="padding: 4px 220px">
        <a-step title="基本信息" />
        <a-step title="仿真方案编制" />
        <a-step title="模拟结果" />
      </a-steps>

      <Basic v-if="step == 0" ref="basicRef" v-bind="$attrs" @saveData="saveData" :inWaterEchoData="inWaterEchoData" />
      <CaseCompile
        v-if="step == 1"
        ref="caseCompileRef"
        :baseInfo="baseInfo"
        :projectFlows="projectFlows"
        @saveData="saveData"
      />
      <Result
        v-if="step == 2"
        ref="resultRef"
        :baseInfo="baseInfo"
        :projectFlows="projectFlows"
        :chSimId="chSimId"
        :isDisabledBtn.sync="isDisabledBtn"
        @saveData="saveData"
        @close="handleClose"
      />
    </div>
    <template slot="footer">
      <a-button @click="cancel" v-if="step === 0">取消</a-button>
      <a-button @click="preStep" v-if="step === 1" :disabled="isDisabledBtn">上一步</a-button>
      <a-button @click="preStep" v-if="step === 2" disabled>上一步</a-button>
      <a-button type="primary" @click.stop="onSubmit" :loading="loading" :disabled="isDisabledBtn">
        {{ step === 2 ? '保存' : '下一步' }}
      </a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getInParameter, saveChSim } from '../../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import Basic from './Basic.vue'
  import CaseCompile from './CaseCompile.vue'
  import Result from './Result.vue'

  export default {
    name: 'AddModal',
    components: { AntModal, Basic, CaseCompile, Result },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '新增',
        step: 0,
        baseInfo: {},
        projectFlows: [],
        chSimId: null, // 渠道水动力id

        isDisabledBtn: false,
        inWaterEchoData: null,
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      preStep() {
        this.step--
      },
      handleClose() {
        this.open = false
        this.$emit('close')
        this.$emit('ok')
      },
      handleShow(params, type) {
        this.open = true

        if (type === 'copy') {
          getInParameter({ chSimId: params.chSimId }).then(res => {
            this.inWaterEchoData = res.data
            console.log('88 water', this.inWaterEchoData)
            this.projectFlows = res.data.projectFlows
          })
        }
      },

      saveData(params) {
        console.log('par 101', params)
        if (!!params) {
          switch (this.step) {
            case 0:
              console.log('par 101 0', params)

              this.baseInfo = params
              this.$nextTick(() => (this.step += 1))
              return
            case 1:
              console.log('par 101 1', params)
              if (params) {
                // params 是 chSimId（渠道水动力id）
                this.chSimId = params
                this.$nextTick(() => (this.step += 1))
              } else {
                this.$message.error('仿真方案生成失败')
              }
              return
            case 2:
              console.log('par 101 2', params)
              //saveChSim({ chSimId: params }).then(res => {
              //  this.$message.success('保存成功', 3)
              //  this.$emit('close')
              //  this.$emit('ok')
              //})
              return
            default:
              return
          }
        }
      },
      onSubmit() {
        switch (this.step) {
          case 0:
            this.$refs.basicRef.save()
            return
          case 1:
            this.$refs.caseCompileRef.save()
            return
          case 2:
            this.$refs.resultRef.save()
            return
          default:
            return
        }
        return
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
</style>
